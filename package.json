{"name": "payer-office-dashboard", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5050", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx src/plugins/iconify/build-icons.js", "postinstall": "npm run build:icons"}, "dependencies": {"@casl/ability": "6.7.3", "@casl/vue": "2.2.2", "@floating-ui/dom": "1.6.8", "@formkit/drag-and-drop": "0.1.6", "@iconify-json/bxl": "1.1.11", "@sindresorhus/is": "7.0.0", "@tiptap/extension-highlight": "^2.5.8", "@tiptap/extension-image": "^2.5.8", "@tiptap/extension-link": "^2.5.8", "@tiptap/extension-text-align": "^2.5.8", "@tiptap/pm": "^2.5.8", "@tiptap/starter-kit": "^2.5.8", "@tiptap/vue-3": "^2.5.8", "@volar/language-server": "2.4.11", "@vue/language-server": "2.2.0", "@vueuse/core": "10.11.0", "@vueuse/math": "10.11.0", "apexcharts": "3.51.0", "axios": "^1.11.0", "chart.js": "4.4.3", "cookie-es": "1.2.2", "eslint-plugin-regexp": "2.6.0", "jwt-decode": "4.0.0", "mapbox-gl": "3.5.2", "moment": "^2.30.1", "ofetch": "1.3.4", "pinia": "2.3.1", "pinia-plugin-persistedstate-2": "^2.0.28", "prismjs": "1.30.0", "roboto-fontface": "0.10.0", "shepherd.js": "13.0.1", "swiper": "11.1.9", "ufo": "1.5.4", "unplugin-vue-define-options": "1.4.9", "vue": "3.5.18", "vue-chartjs": "5.3.1", "vue-flatpickr-component": "11.0.5", "vue-i18n": "9.14.5", "vue-prism-component": "2.0.0", "vue-router": "4.5.1", "vue-toastification": "2.0.0-rc.5", "vue3-apexcharts": "1.5.3", "vue3-perfect-scrollbar": "2.0.0", "vue3-toastify": "^0.2.8", "vuetify": "3.6.14", "webfontloader": "1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "0.43.1", "@antfu/utils": "0.7.10", "@fullcalendar/core": "6.1.19", "@fullcalendar/daygrid": "6.1.19", "@fullcalendar/interaction": "6.1.19", "@fullcalendar/list": "6.1.19", "@fullcalendar/timegrid": "6.1.19", "@fullcalendar/vue3": "6.1.19", "@iconify-json/mdi": "1.2.3", "@iconify-json/ri": "1.2.5", "@iconify/tools": "4.1.2", "@iconify/utils": "2.3.0", "@iconify/vue": "4.3.0", "@intlify/unplugin-vue-i18n": "4.0.0", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.3", "@tiptap/extension-character-count": "^2.5.8", "@tiptap/extension-placeholder": "^2.5.8", "@tiptap/extension-subscript": "^2.5.8", "@tiptap/extension-superscript": "^2.5.8", "@tiptap/extension-underline": "^2.5.8", "@videojs-player/vue": "1.0.0", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.2.0", "eslint": "8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-case-police": "0.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-promise": "6.6.0", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-unicorn": "51.0.1", "eslint-plugin-vue": "9.27.0", "postcss-html": "1.7.0", "postcss-scss": "4.0.9", "sass": "1.76.0", "shiki": "1.12.1", "stylelint": "16.8.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "4.16.5", "unplugin-auto-import": "0.18.2", "unplugin-vue-components": "0.27.3", "unplugin-vue-router": "0.8.8", "video.js": "8.6.0", "vite": "5.4.19", "vite-plugin-vue-devtools": "7.7.0", "vite-plugin-vue-layouts": "0.11.0", "vite-plugin-vuetify": "2.0.3", "vite-svg-loader": "5.1.0", "vue-shepherd": "3.0.0"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7", "sass": "1.76.0"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7", "sass": "1.76.0"}}