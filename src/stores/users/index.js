import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useUserStore = defineStore('User', () => {
  const users = ref(null)
  const user = ref(null)

  function $reset() {
    users.value = null
    user.value = null
  }

  async function getUsers(data) {
    try {
      const res = await axiosIns.get('users', data)

      console.log({ res })

      const usersData = res.data.data || []

      users.value = usersData
    } catch (e) {
      console.log('error')
    }
  }

  async function getSingleUser(data) {
    try {
      const res = await axiosIns.get(`users/${data.id}`, data)
    } catch (e) {

    }
  }

  async function createUser(data) {
    try {
      const res = await axiosIns.post('users', data)

      console.log({ res })

      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'success',
      })
    } catch (e) {
      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'error',
      })
    }
  }

  async function updateUser(data) {
    try {
      const res = await axiosIns.put(`users/${data.id}`, data)

      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'success',
      })
    } catch (e) {
      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'error',
      })
    }
  }

  async function deleteUser(data) {
    try {
      const res = await axiosIns.delete(`users/${data.id}`, data)

      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'success',
      })
    } catch (e) {
      toast('Successfully logged in!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'error',
      })
    }
  }

  return {
    users,
    user,
    $reset,
    getUsers,
    getSingleUser,
    createUser,
    updateUser,
    deleteUser,
  }
}, {
  persistedState: {
    persist: true,
  },
})
