import { defineStore } from 'pinia'
import axiosIns from '@/composables/axios'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

export const useAuthStore = defineStore('Auth', () => {
  const router = useRouter()

  const token = ref(null)
  const user = ref(null)
  const error = ref(null)
  const success = ref(null)
  const forgotError = ref(null)
  const forgotSuccess = ref(null)
  const resetError = ref(null)
  const resetSuccess = ref(null)
  const isFirstLogin = ref(false)
  const twoFactor = ref('false')
  const isLoading = ref(false)
  const logo = ref(null)
  const qrCode = ref(null)
  const recoveryCode = ref(null)

  function $reset() {
    token.value = null
    user.value = null
    error.value = null
    success.value = null
    resetError.value = null
    resetSuccess.value = null
    isFirstLogin.value = false
    isLoading.value = false
  }

  async function login(data) {
    isLoading.value = true

    const res = await axiosIns.post('auth/login', data)

    const tokenData = await res.data.data.token
    const userData =  res.data.data.user

    token.value = tokenData
    user.value = userData

    sessionStorage.setItem('accessToken', tokenData)

    isLoading.value = false

    toast('Successfully logged in!', {
      autoClose: 5000,
      theme: 'dark',
      type: 'success',
    })

    setTimeout(() => {
      router.push('/dashboard')
    }, 2000)
  }

  async function forgotPassword(data) {

    const res = await axiosIns.post('auth/forgot-password', data)

    const forgotError = res.data.errors
    const forgotSuccess = res.data.success

    this.forgotError = forgotError
    this.forgotSuccess = forgotSuccess

    if (res.data.success === true) {
      toast('Successfully sent password reset link!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'success',
      })
    } else if (res.data.success === false) {

      toast(res.data.errors[0], {
        autoClose: 5000,
        theme: 'dark',
        type: 'error',
      })
    }
  }

  async function resetPassword(data) {
    const token = import.meta.env.VITE_APP_TOKEN
    const phrase = import.meta.env.VITE_APP_PHRASE

    const res = await axiosIns.post('auth/reset-password', data)

    const resetError = res.data.errors
    const resetSuccess = res.data.success

    this.resetError = resetError
    this.resetSuccess = resetSuccess

    if (res.data.success === true) {
      toast('Password reset successfully!', {
        autoClose: 5000,
        theme: 'dark',
        type: 'success',
      })

      setTimeout(() => {
        router.replace({ name: 'auth-login' })
      }, 2000)
    } else if (resetSuccess.value === false) {
      toast(res.data.errors, {
        autoClose: 5000,
        theme: 'dark',
        type: 'error',
      })
    }
  }

  async function logout () {
    // const res = await axiosIns.post('auth/login', data)

    toast('Logged Out successfully!', {
      autoClose: 5000,
      theme: 'dark',
      type: 'success',
    })

    setTimeout(() => {
      $reset()
      localStorage.clear()
      sessionStorage.clear()
    }, 2000)
  }

  return {
    token,
    user,
    error,
    success,
    forgotError,
    forgotSuccess,
    resetError,
    resetSuccess,
    isFirstLogin,
    isLoading,
    $reset,
    login,
    forgotPassword,
    resetPassword,
    logout,
  }
}, {
  persistedState: {
    persist: true,
  },
})
