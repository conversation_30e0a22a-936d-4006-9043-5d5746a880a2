import { defineStore } from 'pinia'
import { ref } from 'vue'
import axiosIns from '@/composables/axios'

export const useMerchantStore = defineStore('Merchant', () => {
  const merchants = ref(null)

  function $reset() {
    merchants.value = null
  }

  async function getMerchants (data) {
    try {
      const res = await axiosIns.get('merchants', data)
    } catch (e) {
      console.log('error', e)
    }
  }

  return {
    merchants,
    getMerchants,
  }
}, {
  persistedState: {
    persist: true,
  },
})
