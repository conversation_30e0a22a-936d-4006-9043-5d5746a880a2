import axios from 'axios'

const accessToken = sessionStorage.getItem('accessToken')
const token = import.meta.env.VITE_APP_TOKEN
const phrase = import.meta.env.VITE_APP_PHRASE

const axiosIns = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL,
  headers: {
    'Access-Control-Allow-Origin': ['https://recon-engine.test', 'https://office.gatewaystats.ai', 'https://staging.office.gatewaystats.ai', 'https://reporting.flareapp.io', 'https://*.flareapp.io'],
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, DELETE, PUT',
    'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization',
    Authorization: `Bearer ${accessToken}`,
    'X-API-TOKEN': token,
    'X-API-PASSPHRASE': phrase,
  },
})

axiosIns.interceptors.request.use(function (config) {
  let token = sessionStorage.getItem('accessToken')
  config.headers['Authorization'] = 'Bearer ' + token

  return config
})

export default axiosIns
