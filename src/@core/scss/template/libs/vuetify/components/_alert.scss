// 👉 Alert
@use "@core/scss/base/mixins";
@use "@configured-variables" as variables;

.v-alert {
  .v-alert__content {
    font-size: 0.9375rem;
    font-weight: 400;
    line-height: 1.375rem;

    .v-alert-title {
      margin-block-end: 0.25rem;
    }
  }

  &:not(.v-alert--prominent) .v-alert__prepend {
    border-radius: 0.375rem;
    block-size: 1.875rem;
    inline-size: 1.875rem;

    .v-icon {
      margin: auto;
      block-size: 1.375rem !important;
      font-size: 1.375rem !important;
      inline-size: 1.375rem !important;
    }
  }

  &:not(.v-alert--prominent) {
    &.v-alert--variant-flat,
    &.v-alert--variant-elevated {
      .v-alert__prepend {
        background-color: #fff;

        @include mixins.elevation(2);
      }
    }

    &.v-alert--variant-tonal {
      .v-alert__prepend {
        z-index: 1;
      }
    }
  }

  .v-alert__close {
    .v-btn--icon {
      --v-btn-height: 34px;

      .v-btn__content {
        padding: 0.4375rem;

        .v-icon {
          block-size: 1.25rem;
          font-size: 1.25rem;
          inline-size: 1.25rem;
        }
      }
    }
  }
}

@each $color-name in variables.$theme-colors-name {
  .v-alert {
    &:not(.v-alert--prominent) {
      &.bg-#{$color-name},
      &.text-#{$color-name} {
        .v-alert__prepend .v-icon {
          color: rgb(var(--v-theme-#{$color-name})) !important;
        }
      }

      &.v-alert--variant-tonal {
        &.text-#{$color-name},
        &.bg-#{$color-name} {
          .v-alert__underlay {
            background: rgb(var(--v-theme-#{$color-name})) !important;
          }

          .v-alert__prepend {
            background-color: rgb(var(--v-theme-#{$color-name}));

            .v-icon {
              color: #fff !important;
            }
          }
        }
      }

      &.v-alert--variant-outlined {
        &.text-#{$color-name},
        &.bg-#{$color-name} {
          .v-alert__prepend {
            background-color: rgba(var(--v-theme-#{$color-name}), 0.16);
          }
        }
      }
    }
  }
}
