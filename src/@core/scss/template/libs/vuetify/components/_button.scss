@use "@core/scss/base/mixins";
@use "@configured-variables" as variables;

// 👉 Button
.v-btn {
  &:not(.v-btn--block) {
    min-inline-size: auto;
  }

  &--variant-elevated,
  &--variant-flat {
    &.v-btn--disabled {
      .v-btn__overlay {
        opacity: 0.45;
      }
    }
  }

  // Default (elevated) button
  /* stylelint-disable-next-line no-duplicate-selectors */
  &--variant-elevated,
  &--variant-flat {
    // We want darker background on hover instead of light

    &:hover {
      .v-btn__overlay {
        opacity: 0;
      }
    }

    &:not(.v-btn--loading, .v-btn--disabled) {
      @each $color-name in variables.$theme-colors-name {
        &.bg-#{$color-name} {
          &:hover,
          &:active,
          &:focus {
            background-color: rgb(var(--v-theme-#{$color-name}-darken-1)) !important;
          }
        }
      }
    }
  }

  /* stylelint-disable-next-line no-descending-specificity */
  &--variant-elevated {
    &:active {
      box-shadow: none;
    }
  }

  // Outlined variant
  &--variant-outlined,
  &--variant-text {
    /* stylelint-disable-next-line no-descending-specificity */
    .v-btn__overlay {
      --v-hover-opacity: 0.08;
    }

    &:active {
      .v-btn__overlay {
        opacity: var(--v-hover-opacity);
      }
    }

    &:focus {
      .v-btn__overlay {
        opacity: var(--v-hover-opacity);
      }
    }
  }

  // Tonal variant
  &--variant-tonal {
    &:hover {
      .v-btn__underlay {
        opacity: 0;
      }

      .v-btn__overlay {
        --v-hover-opacity: 0.24;
      }
    }

    &:active {
      .v-btn__overlay {
        --v-hover-opacity: 0.24;

        opacity: var(--v-hover-opacity);
      }

      .v-btn__underlay {
        opacity: 0;
      }
    }

    &:focus {
      .v-btn__overlay {
        --v-hover-opacity: 0.24;

        opacity: var(--v-hover-opacity);
      }

      .v-btn__underlay {
        opacity: 0;
      }
    }
  }

  &:not(.v-btn--icon) .v-icon {
    --v-icon-size-multiplier: 0.7115;

    inline-size: auto;
  }

  &.v-btn--variant-text,
  &.v-btn--variant-plain {
    &:not(.v-btn--icon) {
      padding-inline: 12px;
    }

    &:not(.v-btn--icon).v-btn--size-small {
      padding-inline: 9px;
    }

    &:not(.v-btn--icon).v-btn--size-large {
      padding-inline: 22px;
    }
  }

  // Icon Button
  &--icon.v-btn--density-default {
    block-size: var(--v-btn-height);
    inline-size: var(--v-btn-height);
    padding-inline: 6px;

    &.v-btn--size-default {
      .v-icon {
        --v-icon-size-multiplier: 1 !important;

        block-size: 22px;
        font-size: 22px;
        inline-size: 22px;
      }
    }

    &.v-btn--size-small {
      .v-icon {
        block-size: 20px;
        font-size: 20px;
        inline-size: 20px;
      }
    }

    &.v-btn--size-large {
      .v-icon {
        block-size: 24px;
        font-size: 24px;
        inline-size: 24px;
      }
    }
  }

  // Button Size
  &--size-x-small {
    --v-btn-height: 28px;
    --v-btn-size: 11px;

    &:not(.v-btn--icon) {
      border-radius: 0.125rem;
    }

    line-height: 14px;
    padding-block: 0;
    padding-inline: 10px;
  }

  &--size-small {
    --v-btn-height: 34px;
    --v-btn-size: 13px;

    &:not(.v-btn--icon) {
      border-radius: 0.375rem;
      line-height: 18px;
      padding-block: 0;
      padding-inline: 12px;

      /* stylelint-disable-next-line no-descending-specificity */
      .v-icon {
        --v-icon-size-multiplier: 0.718;
      }

      .v-btn__prepend,
      .v-btn__content > .v-icon--start {
        margin-inline: 0 0.375rem;
      }

      .v-btn__append,
      .v-btn__content > .v-icon--end {
        margin-inline: 0.375rem 0;
      }
    }
  }

  &--size-large {
    --v-btn-height: 42px;
    --v-btn-size: 17px;

    &:not(.v-btn--icon) {
      border-radius: 0.625rem;
      line-height: 26px;
      padding-block: 0;
      padding-inline: 26px;

      /* stylelint-disable-next-line no-descending-specificity */
      .v-icon {
        --v-icon-size-multiplier: 0.7848;
      }

      .v-btn__prepend,
      .v-btn__content > .v-icon--start {
        margin-inline: 0 0.625rem;
      }

      .v-btn__append,
      .v-btn__content > .v-icon--end {
        margin-inline: 0.625rem 0;
      }
    }
  }

  &--size-x-large {
    --v-btn-height: 48px;
    --v-btn-size: 19px;

    &:not(.v-btn--icon) {
      border-radius: 0.625rem;
    }

    line-height: 30px;
    padding-block: 0;
    padding-inline: 26px;
  }

  // Toggle Button
  &-toggle {
    .v-btn {
      border-radius: 0.375rem;
      block-size: 52px !important;
      border-inline-end: none;
      inline-size: 52px !important;

      &.v-btn--density-comfortable {
        block-size: 44px !important;
        inline-size: 44px !important;
      }

      &.v-btn--density-compact {
        block-size: 36px !important;
        inline-size: 36px !important;
      }

      /* stylelint-disable-next-line no-descending-specificity */
      .v-icon {
        block-size: 24px !important;
        color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
        font-size: 24px !important;
        inline-size: 24px !important;
      }

      &--active {
        /* stylelint-disable-next-line no-descending-specificity */
        .v-icon {
          color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
        }
      }
    }

    &.v-btn-group {
      align-items: center;
      padding: 7px;
      border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
      block-size: 66px;

      .v-btn.v-btn--active {
        .v-btn__overlay {
          --v-activated-opacity: 0.08;
        }
      }

      &.v-btn-group--density-compact {
        block-size: 50px;
      }

      &.v-btn-group--density-comfortable {
        block-size: 58px;
      }
    }
  }
}

// 👉 Btn group
.v-btn-group {
  border: none;

  &.v-btn-group--divided .v-btn:not(:last-child) {
    border-inline-end-color: unset;
  }
}
