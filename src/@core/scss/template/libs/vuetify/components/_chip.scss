// 👉 Chip
.v-chip {
  line-height: 1.25rem !important;

  .v-chip__content {
    overflow: hidden;
  }

  &:not(.v-chip--variant-elevated) {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  }

  &.v-chip--variant-elevated {
    background-color: rgba(var(--v-theme-on-surface), var(--v-activated-opacity));
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  }

  .v-chip__close {
    margin-inline: 4px -6px !important;

    .v-icon {
      opacity: 0.7;
    }
  }

  &:not([class*="text-"]) {
    --v-activated-opacity: 0.08;
  }

  &.v-chip--size-small {
    --v-chip-height: 24px !important;

    font-size: 13px !important;
    padding-block: 0 !important;
    padding-inline: 8px !important;

    .v-chip__prepend {
      .v-icon--start {
        font-size: 1rem;
        margin-inline: -4px 4px;
      }

      .v-avatar {
        --v-avatar-height: 16px;
      }

      .v-avatar--start {
        margin-inline: -4px 4px;
      }
    }

    .v-chip__append {
      .v-icon--end {
        font-size: 1rem;
        margin-inline: 4px -4px;
      }

      .v-avatar {
        --v-avatar-height: 16px;
      }

      .v-avatar--end {
        margin-inline: 4px -4px;
      }
    }

    .v-chip__close {
      font-size: 16px;
      margin-inline: 4px -4px !important;
      max-block-size: 16px;
      max-inline-size: 16px;
    }
  }

  &.v-chip--size-default {
    padding-block: 0 !important;
    padding-inline: 12px !important;

    .v-icon {
      font-size: 1.25rem;
    }

    .v-chip__prepend {
      .v-icon--start {
        font-size: 1.25rem;
        margin-inline: -6px 4px;
      }

      .v-avatar {
        --v-avatar-height: 20px;
      }

      .v-avatar--start {
        margin-inline: -6px 4px;
      }
    }

    .v-chip__append {
      .v-icon--end {
        font-size: 1.25rem;
        margin-inline: 4px -6px;
      }

      .v-avatar {
        --v-avatar-height: 20px;
      }

      .v-avatar--end {
        margin-inline: 4px -6px;
      }
    }
  }
}
