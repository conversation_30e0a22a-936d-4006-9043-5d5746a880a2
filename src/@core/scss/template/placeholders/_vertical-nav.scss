@use "@configured-variables" as variables;

%nav-header-action {
  color: rgb(var(--v-theme-on-surface));
  font-size: 0;
}

// ℹ️ Add divider around section title
%vertical-nav-section-title {
  block-size: 2rem;
  font-size: 0.8125rem;
  line-height: 1.125rem;

  /*
    ℹ️ We will use this to add gap between divider and text.
    Moreover, we will use this to adjust the `flex-basis` property of left divider
  */
  $divider-gap: 0.625rem;

  // Thanks: https://stackoverflow.com/a/62359101/10796681
  .title-text {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    column-gap: $divider-gap;

    &::before,
    &::after {
      border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
      content: "";
    }

    &::after {
      flex: 1 1 auto;
    }

    &::before {
      flex: 0 1 0.875rem;
      margin-inline-start: -#{variables.$vertical-nav-horizontal-padding-start};
    }
  }

  // ℹ️ Update the margin-inline-end when vertical nav is in mini state. We done same for link & group.
  @at-root {
    .layout-nav-type-vertical.layout-vertical-nav-collapsed .layout-vertical-nav:not(.hovered) .nav-section-title {
      margin-inline: auto;
    }
  }
}

%vertical-nav-item-interactive {
  border-radius: 0.5rem;

  /* Add pill shape styles */
  block-size: 2.375rem !important;
  margin-block-end: 0.25rem !important;

  /* ℹ️ Wobble effect */
  transition: margin-inline 0.15s ease-in-out;
  will-change: margin-inline;

  // Reduce margin inline end when vertical nav is in collapsed mode and not hovered
  .layout-nav-type-vertical.layout-vertical-nav-collapsed .layout-vertical-nav:not(.hovered) & {
    margin-inline: 0;
  }
}

// Vertical nav item badge styles
%vertical-nav-item-badge {
  font-size: 0.8125rem;
  line-height: 20px;
  padding-block: 0.125rem;
  padding-inline: 0.5rem;
}

// Nav items styles (including section title)
%vertical-nav-item {
  gap: 0.5rem;
  padding-block: 0.5rem;
}

// ℹ️ Icon styling for icon nested inside another nav item (2nd level)
%vertical-nav-items-nested-icon {
  color: rgba(var(--v-theme-on-surface), var(--v-disabled-opacity));
  margin-inline: 0.375rem;
}

%vertical-nav-items-icon-after-2nd-level {
  margin-inline-start: 1rem;
  visibility: visible;
}
