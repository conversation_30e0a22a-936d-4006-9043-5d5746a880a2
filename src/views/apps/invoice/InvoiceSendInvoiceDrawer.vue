<script setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'submit',
])

const emailFrom = ref('<EMAIL>')
const emailTo = ref('<EMAIL>')
const invoiceSubject = ref('Invoice of purchased Admin Templates')

const paymentMessage = ref(`Dear Queen Consolidated,

Thank you for your business, always a pleasure to work with you!

We have generated a new invoice in the amount of $95.59

We would appreciate payment of this invoice by 05/11/2019`)

const onSubmit = () => {
  emit('update:isDrawerOpen', false)
  emit('submit', {
    emailFrom: emailFrom.value,
    emailTo: emailTo.value,
    invoiceSubject: invoiceSubject.value,
    paymentMessage: paymentMessage.value,
  })
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    data-allow-mismatch
    temporary
    location="end"
    :width="400"
    :model-value="props.isDrawerOpen"
    class="scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Send Invoice"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <VForm @submit.prevent="onSubmit">
            <VRow>
              <VCol cols="12">
                <VTextField
                  id="sender-email"
                  v-model="emailFrom"
                  label="From"
                  placeholder="<EMAIL>"
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  id="receiver-email"
                  v-model="emailTo"
                  label="To"
                  placeholder="<EMAIL>"
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  id="invoice-subject"
                  v-model="invoiceSubject"
                  label="Subject"
                  placeholder="Invoice of purchased Admin Templates"
                />
              </VCol>

              <VCol cols="12">
                <VTextarea
                  id="payment-message"
                  v-model="paymentMessage"
                  rows="10"
                  label="Message"
                  placeholder="Thank you for your business, always a pleasure to work with you!"
                />
              </VCol>

              <VCol cols="12">
                <div class="mb-6">
                  <VChip
                    label
                    color="primary"
                    size="small"
                  >
                    <VIcon
                      start
                      icon="ri-links-line"
                    />
                    Invoice Attached
                  </VChip>
                </div>
                <VBtn
                  type="submit"
                  class="me-3"
                >
                  Send
                </VBtn>

                <VBtn
                  color="secondary"
                  variant="outlined"
                  @click="$emit('update:isDrawerOpen', false)"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
