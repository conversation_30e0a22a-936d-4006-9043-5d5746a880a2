<script setup>
const currentTab = ref('window-1')
</script>

<template>
  <div class="d-flex gap-6">
    <div>
      <VTabs
        v-model="currentTab"
        direction="vertical"
        class="v-tabs-pill"
      >
        <VTab>
          <VIcon
            start
            icon="ri-user-line"
          />
          Option 1
        </VTab>

        <VTab>
          <VIcon
            start
            icon="ri-lock-line"
          />
          Option 2
        </VTab>

        <VTab>
          <VIcon
            start
            icon="ri-rfid-line"
          />
          Option 3
        </VTab>
      </VTabs>
    </div>

    <VWindow v-model="currentTab">
      <VWindowItem value="window-1">
        <p>
          Sed aliquam ultrices mauris. Donec posuere vulputate arcu. Morbi ac felis. Etiam feugiat lorem non metus. Sed a libero.
        </p>

        <p class="mb-0">
          Phasellus dolor. Fusce neque. Fusce fermentum odio nec arcu. Pellentesque libero tortor, tincidunt et.
        </p>
      </VWindowItem>

      <VWindowItem value="window-2">
        <p class="mb-0">
          Morbi nec metus. Suspendisse faucibus, nunc et pellentesque egestas, lacus ante convallis tellus, vitae iaculis lacus elit id tortor. Sed mollis, eros et ultrices tempus, mauris ipsum aliquam libero, non adipiscing dolor urna a orci. Curabitur ligula sapien, tincidunt non, euismod vitae, posuere imperdiet, leo. Nunc sed turpis.
        </p>
      </VWindowItem>

      <VWindowItem value="window-3">
        <p class="mb-0">
          Fusce a quam. Phasellus nec sem in justo pellentesque facilisis. Nam eget dui. Proin viverra, ligula sit amet ultrices semper, ligula arcu tristique sapien, a accumsan nisi mauris ac eros. In dui magna, posuere eget, vestibulum et, tempor auctor, justo.
        </p>
      </VWindowItem>
    </VWindow>
  </div>
</template>
