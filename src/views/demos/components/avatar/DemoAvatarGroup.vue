<script setup>
import avatar1 from '@images/avatars/avatar-1.png'
import avatar2 from '@images/avatars/avatar-2.png'
import avatar3 from '@images/avatars/avatar-3.png'
import avatar4 from '@images/avatars/avatar-4.png'
import avatar5 from '@images/avatars/avatar-5.png'
import avatar6 from '@images/avatars/avatar-6.png'
</script>

<template>
  <div class="v-avatar-group demo-avatar-group">
    <VAvatar>
      <VImg :src="avatar1" />
      <VTooltip
        activator="parent"
        location="top"
      >
        <PERSON>
      </VTooltip>
    </VAvatar>

    <VAvatar>
      <VImg :src="avatar2" />
      <VTooltip
        activator="parent"
        location="top"
      >
        <PERSON><PERSON>
      </VTooltip>
    </VAvatar>

    <VAvatar>
      <VImg :src="avatar3" />
      <VTooltip
        activator="parent"
        location="top"
      >
        <PERSON>
      </VTooltip>
    </VAvatar>

    <VAvatar>
      <VImg :src="avatar4" />
      <VTooltip
        activator="parent"
        location="top"
      >
        <PERSON>
      </VTooltip>
    </VAvatar>

    <VAvatar>
      <VImg :src="avatar5" />
      <VTooltip
        activator="parent"
        location="top"
      >
        Scott Wells
      </VTooltip>
    </VAvatar>

    <VAvatar>
      <VImg :src="avatar6" />
      <VTooltip
        activator="parent"
        location="top"
      >
        Angel Bishop
      </VTooltip>
    </VAvatar>

    <VAvatar :color="$vuetify.theme.current.dark ? '#383B55' : '#F0EFF0'">
      +3
    </VAvatar>
  </div>
</template>

<style lang="scss">
.demo-avatar-group{
  &.v-avatar-group {
    .v-avatar{
      &:last-child{
        border: none;
      }
    }
  }
}
</style>
