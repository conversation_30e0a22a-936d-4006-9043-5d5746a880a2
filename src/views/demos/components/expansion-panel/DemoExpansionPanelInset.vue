<template>
  <VExpansionPanels variant="inset">
    <VExpansionPanel
      v-for="item in 4"
      :key="item"
    >
      <VExpansionPanelTitle>Inset {{ item }}</VExpansionPanelTitle>
      <VExpansionPanelText>
        Chocolate bar sweet roll chocolate cake pastry I love gummi bears pudding chocolate cake. I love brownie powder apple pie sugar plum I love cake candy canes wafer. Tiramisu I love oat cake oat cake danish icing. Dessert sugar plum sugar plum cookie donut chocolate cake oat cake I love gummi bears.
      </VExpansionPanelText>
    </VExpansionPanel>
  </VExpansionPanels>
</template>
