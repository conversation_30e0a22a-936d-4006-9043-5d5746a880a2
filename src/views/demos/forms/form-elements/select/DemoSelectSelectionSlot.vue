<script setup>
import avatar1 from '@images/avatars/avatar-1.png'
import avatar2 from '@images/avatars/avatar-2.png'
import avatar3 from '@images/avatars/avatar-3.png'
import avatar4 from '@images/avatars/avatar-4.png'
import avatar5 from '@images/avatars/avatar-5.png'

const items = [
  {
    name: '<PERSON>',
    avatar: avatar1,
  },
  {
    name: '<PERSON>',
    avatar: avatar2,
  },
  {
    name: '<PERSON>',
    avatar: avatar3,
  },
  {
    name: '<PERSON>',
    avatar: avatar4,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    avatar: avatar5,
  },
]

const value = ref(['<PERSON>'])
</script>

<template>
  <VSelect
    v-model="value"
    :items="items"
    item-title="name"
    item-value="name"
    label="Select Item"
    placeholder="Select Item"
    multiple
    clearable
    clear-icon="ri-close-line"
  >
    <template #selection="{ item }">
      <VChip>
        <template #prepend>
          <VAvatar
            start
            :image="item.raw.avatar"
          />
        </template>

        <span>{{ item.title }}</span>
      </VChip>
    </template>
  </VSelect>
</template>
