<script setup>
import { useAuthStore } from '@stores/auth'
import { useUserStore } from '@stores/users'
import { useMerchantStore } from '@stores/merchants'

const store = useAuthStore()
const userStore = useUserStore()
const merchantStore = useMerchantStore()

const { user } = storeToRefs(store)
const { users } = storeToRefs(userStore)
const { merchants } = storeToRefs(merchantStore)

onMounted(() => {
  if (!users.value) {
    getUsers()
  }
  if (!merchants.value) {
    getMerchants()
  }
})

const createDialog = ref(false)
const editDialog = ref(false)
const deleteDialog = ref(false)

const form = ref({
  name: '',
  email: '',
  password: '',
})

const selectedUser = ref(null)

const headers = [
  {
    title: 'NAME',
    key: 'name',
  },
  {
    title: 'EMAIL',
    key: 'email',
  },
  {
    title: 'ACTIONS',
    key: 'actions',
  },
]

const resolveStatusVariant = status => {
  if (status === 1)
    return {
      color: 'primary',
      text: 'Current',
    }
  else if (status === 2)
    return {
      color: 'success',
      text: 'Professional',
    }
  else if (status === 3)
    return {
      color: 'error',
      text: 'Rejected',
    }
  else if (status === 4)
    return {
      color: 'warning',
      text: 'Resigned',
    }
  else
    return {
      color: 'info',
      text: 'Applied',
    }
}

const getUsers = () => {
  userStore.getUsers({
    user: user.value.id,
  })
}

const getMerchants = () => {
  merchantStore.getMerchants({
    user: user.value.id,
  })
}

const createUser = () => {
  userStore.createUser({
    user: user.value.id,
    name: form.value.name,
    email: form.value.email,
  })
  createDialog.value = true
}

const editUser = item => {
  selectedUser.value = item
  editDialog.value = true
}

const updateUser = item => {
  userStore.updateUser({
    user: user.value.id,
    name: selectedUser.value.name,
    email: selectedUser.value.email,
  })
  editDialog.value = false
}

const deleteUser = () => {
  deleteDialog.value = true
}

const closeDelete = () => {
  deleteDialog.value = false
}

const deleteItemConfirm = () => {
  deleteDialog.value = false
}
</script>

<template>
  <VCard
    v-if="users"
    title="User Management"
    class="pt-5"
  >
    <VCardText class="text-right">
      <VBtn @click="createDialog = !createDialog">
        Create User
      </VBtn>
    </VCardText>
    <VCardText class="mt-5">
      <VDataTable
        :headers="headers"
        :items="users"
        :items-per-page="5"
        class="text-no-wrap"
      >
        <!-- Actions -->
        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <IconBtn
              size="small"
              @click="editUser(item)"
            >
              <VIcon icon="ri-pencil-line" />
            </IconBtn>
            <IconBtn
              size="small"
              @click="deleteUser(item)"
            >
              <VIcon icon="ri-delete-bin-line" />
            </IconBtn>
          </div>
        </template>
      </VDataTable>
    </VCardText>
  </VCard>

  <!-- 👉 Create Dialog  -->
  <VDialog
    v-model="createDialog"
    max-width="600px"
  >
    <VCard title="Create Item">
      <VCardText>
        <VRow>
          <!-- fullName -->
          <VCol
            cols="12"
            sm="6"
          >
            <VTextField
              v-model="form.name"
              label="User name"
            />
          </VCol>

          <!-- email -->
          <VCol
            cols="12"
            sm="6"
          >
            <VTextField
              v-model="form.email"
              label="Email"
            />
          </VCol>

          <!-- password -->
          <VCol
            cols="12"
            sm="6"
          >
            <VTextField
              v-model="form.password"
              type="password"
              label="Password"
            />
          </VCol>
        </VRow>
      </VCardText>

      <VCardText>
        <div class="self-align-end d-flex gap-4 justify-end">
          <VBtn
            color="error"
            variant="outlined"
            @click="createDialog = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            variant="elevated"
            @click="createUser"
          >
            Create User
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </VDialog>

  <!-- 👉 Edit Dialog  -->
  <VDialog
    v-model="editDialog"
    max-width="600px"
  >
    <VCard title="Edit Item">
      <VCardText>
        <div class="text-body-1 mb-6">
          Name: <span class="text-h6">{{ selectedUser?.name }}</span>
        </div>
        <VRow>
          <!-- fullName -->
          <VCol
            cols="12"
            sm="6"
          >
            <VTextField
              v-model="selectedUser.name"
              label="User name"
            />
          </VCol>

          <!-- email -->
          <VCol
            cols="12"
            sm="6"
          >
            <VTextField
              v-model="selectedUser.email"
              label="Email"
            />
          </VCol>
        </VRow>
      </VCardText>

      <VCardText>
        <div class="self-align-end d-flex gap-4 justify-end">
          <VBtn
            color="error"
            variant="outlined"
            @click="editDialog = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            variant="elevated"
            @click="updateUser"
          >
            Update User
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </VDialog>

  <!-- 👉 Delete Dialog  -->
  <VDialog
    v-model="deleteDialog"
    max-width="500px"
  >
    <VCard title="Are you sure you want to delete this item?">
      <VCardText>
        <div class="d-flex justify-center gap-4">
          <VBtn
            color="primary"
            variant="outlined"
            @click="closeDelete"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            variant="elevated"
            @click="deleteItemConfirm"
          >
            Confirm Delete
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped lang="scss">
/**/
</style>
