import { setupLayouts } from 'virtual:generated-layouts'
import { createRouter, createWebHistory } from 'vue-router/auto'
import { isAuthenticated } from './auth'

function recursiveLayouts(route) {
  if (route.children) {
    for (let i = 0; i < route.children.length; i++)
      route.children[i] = recursiveLayouts(route.children[i])
    
    return route
  }
  
  return setupLayouts([route])[0]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to) {
    if (to.hash)
      return { el: to.hash, behavior: 'smooth', top: 60 }
    
    return { top: 0 }
  },
  extendRoutes: pages => [
    ...[...pages].map(route => recursiveLayouts(route)),
  ],
})

router.beforeEach((to, from, next) => {
  console.log('Router guard triggered:', {
    path: to.path,
    requiresAuth: to.meta.requiresAuth,
    isAuthenticated: isAuthenticated(),
    fullPath: to.fullPath
  })

  if (to.meta.requiresAuth && !isAuthenticated()) {
    console.log('Redirecting to login...')
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export { router }
export default function (app) {
  app.use(router)
}
